const { SendNormalRequest } = require('./request');

let deviceId = '';

function reportAdECPM(appid, reportName) {
    SendNormalRequest('GET', `https://stateless.unity.cn/release/1156179e-6f72-4b30-940a-8f7fca6efaa5/ad_report?appid_key=${appid}&report_name=${reportName}`)
        .then(() => { })
        .catch(() => { });
}

function reportAdValue(appid, adType, ecpm) {
    SendNormalRequest('GET', `https://stateless.unity.cn/release/1156179e-6f72-4b30-940a-8f7fca6efaa5/ecpm_cal?appid_key=${appid}&ad_type=${adType}&ecpm_value=${ecpm}&device_id=${deviceId}`)
        .then(() => { })
        .catch(() => { });
}

export function initDeviceId() {
    try {
        if (window['qg'].getSystemInfoSync().platformVersionCode >= 1096) {
            window['qg'].getDeviceId({
                success: function (data) {
                    console.log(`handling success: ${data.deviceId}`);
                    deviceId = data.deviceId;
                },
                fail: function (data, code) {
                    console.error(`handling fail, code = ${code}`);
                    reportAdECPM('nan-uc', 'get-device-id-fail');
                },
            });
        } else {
            reportAdECPM('nan-uc', 'no-device-id');
        }
    } catch (error) {
        console.error('getDeviceId error', error);
        reportAdECPM('nan-uc', 'device-id-error');
    }
}

export function reportAdShow(res) {
    try {
        if (res == null) {
            console.log('res为null');
            return;
        }
        if (cc.sys.localStorage.getItem('channel') != "uc") {
            console.log('不是uc渠道');
            return;
        }
        const ecpmPrice = res.ECPM && !isNaN(res.ECPM.ECPMPrice) ? res.ECPM.ECPMPrice : 0;
        if (ecpmPrice == 0) {
            console.log('ecpmPrice为0');
            return;
        }
        const appid = cc.sys.localStorage.getItem('appid') || 'default';
        SendNormalRequest('GET', `https://stateless.unity.cn/release/1156179e-6f72-4b30-940a-8f7fca6efaa5/ecpm_cal?appid_key=${appid}&ad_type=video_show&device_id=${deviceId}&ecpm_value=${ecpmPrice}`)
            .then(() => { })
            .catch(() => { });
        const strategy = cc.sys.localStorage.getItem('strategy') || '0';
        if (strategy == '0') {
            return;
        }
        checkCallback(appid, ecpmPrice, strategy);
    } catch (error) {
        console.error('reportAdShow error', error);
    }
}

/**
 * 处理广告ECPM并计算累计ARPU值
 * @param {object} res 广告返回结果对象，包含ECPM信息
 * @returns {boolean} 是否达到配置的ARPU阈值
 */
export function handleAdECPM(res, type) {
    if (res == null) {
        console.log('res为null');
        return;
    }

    if (cc.sys.localStorage.getItem('channel') != "uc") {
        console.log('不是uc渠道');
        return;
    }

    if (type != 'video') {
        console.log('不是视频广告');
        return;
    }

    // 确保ECPM.ECPMPrice是有效数字
    const ecpmPrice = res.ECPM && !isNaN(res.ECPM.ECPMPrice) ? res.ECPM.ECPMPrice : 0;
    if (ecpmPrice == 0) {
        console.log('ecpmPrice为0');
        return;
    }
    const appid = cc.sys.localStorage.getItem('appid') || 'default';
    reportAdValue(appid, type, ecpmPrice);
    const strategy = cc.sys.localStorage.getItem('strategy') || '0';
    if (strategy == '1') {
        return;
    }
    checkCallback(appid, ecpmPrice, strategy);
}

function checkCallback(appid, ecpmPrice, strategy) {
    if (cc.sys.localStorage.getItem('shave_callback') == "1") {
        console.log('shave计算中');
        return;
    }
    cc.sys.localStorage.setItem('shave_callback', "1");

    const cold_start = cc.sys.localStorage.getItem('cold_start') || '1';
    const uctrackid = cc.sys.localStorage.getItem('uctrackid') || '';

    cc.sys.localStorage.setItem('cold_start', "0");
    try {
        // 发送请求到指定URL并处理响应
        SendNormalRequest('GET', `https://stateless.unity.cn/release/1156179e-6f72-4b30-940a-8f7fca6efaa5/vivo_shave?appid_key=${appid}&device_id=${deviceId}&uctrackid=${uctrackid}&cold_start=${cold_start}&ecpm_value=${ecpmPrice}&strategy=${strategy}`)
            .then(responseText => {
                try {
                    const response = JSON.parse(responseText);
                    if (response.statusCode === 200) {
                        const event_time = Date.now(); // 获取当前时间戳（毫秒）
                        if (response.shave === true) {
                            console.log('请求成功且shave为true');
                            // 在这里可以添加额外的业务逻辑处理
                            const callbackUrl = `https://huichuan.uc.cn/callback/appapi?uctrackid=${uctrackid}&type=67&event_time=${event_time}`;
                            SendNormalRequest('GET', callbackUrl)
                                .then(callbackResponse => {
                                    console.log('回传请求成功:', callbackResponse);
                                    reportAdECPM(appid, 'report-uc-success');
                                    cc.sys.localStorage.setItem('shave_callback', "0");
                                })
                                .catch(callbackError => {
                                    console.error('回传请求失败:', callbackError);
                                    reportAdECPM(appid, 'report-uc-fail');
                                    cc.sys.localStorage.setItem('shave_callback', "0");
                                });
                        } else {
                            console.log('请求成功但shave为false');
                            SendNormalRequest('GET', `https://stateless.unity.cn/release/1156179e-6f72-4b30-940a-8f7fca6efaa5/ad_report?appid_key=${appid}&report_name=report-uc-dont&uctrackid=${uctrackid}&event_time=${event_time}&device_id=${deviceId}`)
                                .then(() => { })
                                .catch(() => { });
                            cc.sys.localStorage.setItem('shave_callback', "0");
                        }
                    } else {
                        console.log('请求成功但状态异常:', response);
                        reportAdECPM(appid, 'shave-response-error');
                        cc.sys.localStorage.setItem('shave_callback', "0");
                    }
                } catch (parseError) {
                    console.error('解析响应数据失败:', parseError);
                    reportAdECPM(appid, 'shave-parse-error');
                    cc.sys.localStorage.setItem('shave_callback', "0");
                }
            })
            .catch(error => {
                console.error('请求失败:', error);
                reportAdECPM(appid, 'shave-request-error');
                cc.sys.localStorage.setItem('shave_callback', "0");
            });
    } catch (error) {
        console.log('handleAdECPM error', error);
        cc.sys.localStorage.setItem('shave_callback', "0");
    }
}