import { handleAdECPM, reportAdShow } from "./adBusinessHandler";
import { getIsFullInsert, getIsHalfInsert } from "./configGetter";
import { umaEvent } from "./umaUtils";
import { SendNormalRequest } from "./request";

let rewardAdUnitId = "2235730";
let nativeAdUnitId = "2235729";
let gridAdUnitId = "2235731";
let bannerAdUnitId = "2235727";
let normalBannerAdUnitId = "2235728";
let gameBannerUnitId = "2235732";
let gameDrawerUnitId = "2235733";
let fullInsertUnitId = "2235734";
let halfInsertUnitId = "2235735";

let bannerAd = null;
let gameBannerAd = null;
let fullNativeAdShow = false;
let bannerAdShowed = false;

let isRewardShowed = false;

export function initAd() {
    var ldyRewardAdUnitId = cc.sys.localStorage.getItem('adid') || '';
    if (ldyRewardAdUnitId) {
        rewardAdUnitId = ldyRewardAdUnitId;
    }
}

export function showBannerAd() {
    try {
        bannerAd = window['qg'].createBannerAd({
            adUnitId: bannerAdUnitId
        });
        umaEvent('bannerAdShow');
        bannerAdShowed = true;
        bannerAd.offError();
        var loadRes = null;

        bannerAd.offLoad();
        bannerAd.onLoad((res) => {
            console.log('bannerAd.onLoad', JSON.stringify(res));
        });
        bannerAd.offClick();
        bannerAd.onClick((clickRes) => {
            console.log('bannerAd.onClick', JSON.stringify(clickRes));
            handleAdECPM(loadRes, 'banner');
            loadRes = null;
        });

        if (isBindingEnabled()) {
            try {
                bannerAd.offGetAdECPM();
                bannerAd.offNotifyAdRankWin();

                bannerAd.onGetAdECPM((ecpmRes) => {
                    console.log('bannerAd.onGetAdECPM', JSON.stringify(ecpmRes));
                    validateAdECPMResponse(ecpmRes, 'banner');
                    loadRes = ecpmRes;
                });
                bannerAd.getAdECPM();
                bannerAd.onNotifyAdRankWin((res) => {
                    console.log('bannerAd.onNotifyAdRankWin', JSON.stringify(res));
                    validateNotifyAdRankWinResponse(res, 'banner');
                });
                bannerAd.notifyAdRankWin({
                    lossPrice: 1000,
                });
            } catch (error) {
                console.log('bannerAd.getAdECPM error', JSON.stringify(error));
                reportErrorEvent('banner-binding-error');
            }
        }
        bannerAd.show();
        bannerAd.onError(err => {
            umaEvent('bannerAdError', { error: JSON.stringify(err) });
            showNormalBannerAd();
        });
    } catch (error) {
        bannerAdShowed = false;
        umaEvent('bannerAdError', { error: String(error) });
        showNormalBannerAd();
    }
}

export function showNormalBannerAd() {
    try {
        bannerAd = window['qg'].createBannerAd({
            adUnitId: normalBannerAdUnitId
        });
        umaEvent('normalBannerAdShow');
        var loadRes = null;

        bannerAd.offError();
        bannerAd.offLoad();
        bannerAd.onLoad((res) => {
            console.log('normalBannerAd.onLoad', JSON.stringify(res));
        });
        bannerAd.offClick();
        bannerAd.onClick((clickRes) => {
            console.log('bannerAd.onClick', JSON.stringify(clickRes));
            handleAdECPM(loadRes, 'banner');
            loadRes = null;
        });

        bannerAdShowed = true;

        if (isBindingEnabled()) {
            try {
                bannerAd.offGetAdECPM();
                bannerAd.offNotifyAdRankWin();

                bannerAd.onGetAdECPM((ecpmRes) => {
                    console.log('bannerAd.onGetAdECPM', JSON.stringify(ecpmRes));
                    validateAdECPMResponse(ecpmRes, 'normal_banner');
                    loadRes = ecpmRes;
                });
                bannerAd.getAdECPM();
                bannerAd.onNotifyAdRankWin((res) => {
                    console.log('bannerAd.onNotifyAdRankWin', JSON.stringify(res));
                    validateNotifyAdRankWinResponse(res, 'normal_banner');
                });
                bannerAd.notifyAdRankWin({
                    lossPrice: 1000,
                });
            } catch (error) {
                console.log('bannerAd.getAdECPM error', JSON.stringify(error));
                reportErrorEvent('banner-binding-error');
            }
        }
        bannerAd.show();
        bannerAd.onError(err => {
            umaEvent('normalBannerAdError', { error: JSON.stringify(err) });
            showGameBanner();
        });
    } catch (error) {
        bannerAdShowed = false;
        umaEvent('normalBannerAdError', { error: String(error) });
        showGameBanner();
    }
}

export function hideBannerAd() {
    try {
        if (bannerAd) {
            bannerAd.hide();
            bannerAd.destroy();
            bannerAdShowed = false;
        }
        if (gameBannerAd) {
            gameBannerAd.hide();
            gameBannerAd.destroy();
        }
    } catch (error) { }
}

export function isBannerAdShowed() {
    return bannerAdShowed;
}

export function isRewardVideShowed() {
    return isRewardShowed;
}

export function showVideoAd(adName, info, onSuccess, onFailure, needFallback = true) {
    console.log('showVideoAd adName =', adName);
    let adInfo = info ? info : '';
    try {
        var videoAd = window['qg'].createRewardedVideoAd({ adUnitId: rewardAdUnitId });

        var loadRes = null;
        const handleClose = (res) => {
            if (!res || res.isEnded) {
                if (onSuccess) {
                    onSuccess()
                }
                onSuccess = () => { }
                onFailure = () => { }
                umaEvent('rewardAdSuccess', { adName: adName, adInfo: adInfo });
            } else {
                console.log('广告没看完');
                umaEvent('rewardAdFailed', { adName: adName, adInfo: adInfo });
                if (onFailure) {
                    onFailure();
                }
                onSuccess = () => { }
                onFailure = () => { }
            }
            isRewardShowed = false;
        };

        videoAd.offLoad();
        videoAd.offError();
        videoAd.offClose();
        videoAd.offClick();

        videoAd.load();
        videoAd.onLoad((res) => {
            if (isBindingEnabled()) {
                try {
                    videoAd.offGetAdECPM();
                    videoAd.offNotifyAdRankWin();

                    videoAd.onGetAdECPM((ecpmRes) => {
                        console.log('videoAd.onGetAdECPM', JSON.stringify(ecpmRes));
                        validateAdECPMResponse(ecpmRes, 'video');
                        loadRes = ecpmRes;
                    });
                    videoAd.getAdECPM();
                    videoAd.onNotifyAdRankWin((res) => {
                        console.log('videoAd.onNotifyAdRankWin', JSON.stringify(res));
                        validateNotifyAdRankWinResponse(res, 'video');
                    });
                    videoAd.notifyAdRankWin({
                        lossPrice: 1000,
                    });
                } catch (error) {
                    console.log('videoAd.getAdECPM error', JSON.stringify(error));
                    reportErrorEvent('video-binding-error');
                    loadRes = res;
                }
            } else if (isSwitchEnableVide()) {
                loadRes = res;
            }
            isRewardShowed = true;
            reportAdShow(loadRes);
            videoAd.show();
            umaEvent('rewardAdShow', { adName: adName, adInfo: adInfo });
        });
        videoAd.onError((err) => {
            umaEvent('rewardAdError', { error: JSON.stringify(err) });
            if (getIsFullInsert()) {
                showFullInsertAd(adName, adInfo, () => {
                    if (onSuccess) {
                        onSuccess()
                    }
                    onSuccess = () => { }
                    onFailure = () => { }
                }, () => {
                    if (onFailure) {
                        onFailure();
                    }
                    onSuccess = () => { }
                    onFailure = () => { }
                });
            } else {
                if (onFailure) {
                    onFailure();
                }
                onSuccess = () => { }
                onFailure = () => { }
                showNativeAd("reward_fallback", '', 0, 0.5, false);
            }
            isRewardShowed = false;
        });
        videoAd.onClose(handleClose);
        videoAd.onClick((clickRes) => {
            console.log('videoAd.onClick', JSON.stringify(clickRes));
            handleAdECPM(loadRes, 'video');
            loadRes = null;
        });
    } catch (error) {
        umaEvent('rewardAdError', { error: String(error) });
        if (getIsFullInsert()) {
            showFullInsertAd(adName, adInfo, () => {
                if (onSuccess) {
                    onSuccess()
                }
                onSuccess = () => { }
                onFailure = () => { }
            }, () => {
                if (onFailure) {
                    onFailure();
                }
                onSuccess = () => { }
                onFailure = () => { }
            });
        } else {
            if (onFailure) {
                onFailure();
            }
            onSuccess = () => { }
            onFailure = () => { }
            showNativeAd("reward_fallback", '', 0, 0.5, false);
        }
        isRewardShowed = false;
    }
}

export function showGridAd() { }

export function showNativeAd(name, info = '', positionType = 0, position = 0.5, needFallback = true) {
    console.log('showNativeAd adName =', name);
    let adInfo = info ? info : '';
    try {
        var customAd = null;
        if (positionType == 1) {
            const { screenWidth, screenHeight } = window['qg'].getSystemInfoSync();
            customAd = window['qg'].createCustomAd({
                adUnitId: nativeAdUnitId,
                style: {
                    top: 0,
                    left: 0,
                    width: screenWidth,
                },
            });
        } else if (positionType == 2) {
            const { screenWidth, screenHeight } = window['qg'].getSystemInfoSync();
            customAd = window['qg'].createCustomAd({
                adUnitId: nativeAdUnitId,
                style: {
                    width: screenWidth,
                },
            });
        } else {
            const { screenWidth, screenHeight } = window['qg'].getSystemInfoSync();
            const adTop = screenHeight * position;

            const width = screenWidth * (3 / 4);
            const left = (screenWidth - width) / 2;

            customAd = window['qg'].createCustomAd({
                adUnitId: nativeAdUnitId,
                style: {
                    top: adTop,
                    left: left,
                    width: width,
                },
            });
        }

        customAd.offShow();
        customAd.offError();
        customAd.offLoad();

        var loadRes = null;
        customAd.onLoad((res) => {
            console.log('customAd.onLoad', JSON.stringify(res));
        });
        if (isBindingEnabled()) {
            try {
                customAd.offGetAdECPM();
                customAd.offNotifyAdRankWin();

                customAd.onGetAdECPM((ecpmRes) => {
                    console.log('customAd.onGetAdECPM', JSON.stringify(ecpmRes));
                    validateAdECPMResponse(ecpmRes, 'native');
                    loadRes = ecpmRes;
                });
                customAd.getAdECPM();
                customAd.onNotifyAdRankWin((res) => {
                    console.log('customAd.onNotifyAdRankWin', JSON.stringify(res));
                    validateNotifyAdRankWinResponse(res, 'native');
                });
                customAd.notifyAdRankWin({
                    lossPrice: 1000,
                });
            } catch (error) {
                console.log('customAd.getAdECPM error', JSON.stringify(error));
                reportErrorEvent('customAd-binding-error');
            }
        }
        customAd.show();
        customAd.onShow(() => {
            umaEvent('nativeAdShow', { adName: name, adInfo: adInfo });
        });
        customAd.onError((err) => {
            umaEvent('nativeAdError', { error: JSON.stringify(err) });
            if (getIsHalfInsert() && needFallback) {
                showHalfInsertAd(name, adInfo, null, null, false);
            }
        });

        customAd.offClick();
        customAd.onClick((clickRes) => {
            console.log('nativeAd.onClick', JSON.stringify(clickRes));
            handleAdECPM(loadRes, 'native');
            loadRes = null;
        });
    } catch (error) {
        umaEvent('nativeAdError', { error: String(error) });
        if (getIsHalfInsert() && needFallback) {
            showHalfInsertAd(name, adInfo, null, null, false);
        }
    }
}

export function showGameBanner() {
    try {
        if (!gameBannerAd) {
            gameBannerAd = window['qg'].createGameBannerAd({
                adUnitId: gameBannerUnitId
            });
        }
        gameBannerAd.offShow();
        gameBannerAd.offError();
        gameBannerAd.show();
        gameBannerAd.onShow(() => {
            umaEvent('gameBannerAdShow');
        });
        gameBannerAd.onError((err) => {
            umaEvent('gameBannerAdError', { error: String(error) });
        });
    } catch (error) {
        umaEvent('gameBannerAdError', { error: String(error) });
    }
}

export function showDrawerAD() {
    try {
        if (window['qg'].getSystemInfoSync().platformVersionCode >= 1090) {
            const { screenWidth, screenHeight } = window['qg'].getSystemInfoSync();
            var gameDrawerAd = window['qg'].createGameDrawerAd({
                adUnitId: gameDrawerUnitId,
                style: {
                    top: screenHeight / 3,
                },
            });
            gameDrawerAd.show();
            umaEvent('gameDrawerAdShow');
        } else {
            console.log("快应用平台版本号低于1090，暂不支持互推盒子相关 API");
        }
    } catch (error) {
        umaEvent('gameDrawerAdError', { error: String(error) });
    }
}

function showFullInsertAd(name, info = '', onAdClose, onError) {
    console.log('showFullInsertAd adName =', name);
    try {
        var insertAd = window['qg'].createInterstitialAd({
            adUnitId: fullInsertUnitId
        });
        insertAd.offLoad();
        insertAd.offError();
        insertAd.offClose();

        insertAd.load();
        var loadRes = null;
        insertAd.onLoad((res) => {
            if (isBindingEnabled()) {
                try {
                    insertAd.offGetAdECPM();
                    insertAd.offNotifyAdRankWin();

                    insertAd.onGetAdECPM((ecpmRes) => {
                        console.log('fullInsertAd.onGetAdECPM', JSON.stringify(ecpmRes));
                        validateAdECPMResponse(ecpmRes, 'full_insert');
                        loadRes = ecpmRes;
                    });
                    insertAd.getAdECPM();
                    insertAd.onNotifyAdRankWin((res) => {
                        console.log('fullInsertAd.onNotifyAdRankWin', JSON.stringify(res));
                        validateNotifyAdRankWinResponse(res, 'full_insert');
                    });
                    insertAd.notifyAdRankWin({
                        lossPrice: 1000,
                    });
                } catch (error) {
                    console.log('fullInsertAd.getAdECPM error', JSON.stringify(error));
                    reportErrorEvent('full-insert-binding-error');
                    loadRes = res;
                }
            } else if (isSwitchEnableVide()) {
                loadRes = res;
            }
            isRewardShowed = true;
            insertAd.show();
            umaEvent('fullInsterAdShow', { adName: name, adInfo: info });
        });
        insertAd.onError((error) => {
            umaEvent('fullInsterAdError', { error: JSON.stringify(error) });
            showHalfInsertAd(name, info, onAdClose, onError, true);
            isRewardShowed = false;
        });
        insertAd.onClose(() => {
            if (onAdClose) {
                onAdClose();
            }
            onAdClose = () => { };
            umaEvent('fullInsterAdClose', { adName: name, adInfo: info });
            isRewardShowed = false;
        });

        insertAd.offClick();
        insertAd.onClick((clickRes) => {
            console.log('fullInsertAd.onClick', JSON.stringify(clickRes));
            handleAdECPM(loadRes, 'full_insert');
            loadRes = null;
        });
    } catch (error) {
        umaEvent('fullInsterAdError', { error: JSON.stringify(error) });
        showHalfInsertAd(name, info, onAdClose, onError, true);
        isRewardShowed = false;
    }
}

function showHalfInsertAd(name, info = '', onAdClose, onError, needFallback = false) {
    console.log('showHalfInsertAd adName =', name);
    try {
        var insertAd = window['qg'].createInterstitialAd({
            adUnitId: halfInsertUnitId
        });
        insertAd.offLoad();
        insertAd.offError();
        insertAd.offClose();

        insertAd.load();
        var loadRes = null;
        insertAd.onLoad((res) => {
            if (isBindingEnabled()) {
                try {
                    insertAd.offGetAdECPM();
                    insertAd.offNotifyAdRankWin();

                    insertAd.onGetAdECPM((ecpmRes) => {
                        console.log('halfInsertAd.onGetAdECPM', JSON.stringify(ecpmRes));
                        validateAdECPMResponse(ecpmRes, 'half_insert');
                        loadRes = ecpmRes;
                    });
                    insertAd.getAdECPM();
                    insertAd.onNotifyAdRankWin((res) => {
                        console.log('halfInsertAd.onNotifyAdRankWin', JSON.stringify(res));
                        validateNotifyAdRankWinResponse(res, 'half_insert');
                    });
                    insertAd.notifyAdRankWin({
                        lossPrice: 1000,
                    });
                } catch (error) {
                    console.log('halfInsertAd.getAdECPM error', JSON.stringify(error));
                    reportErrorEvent('half-insert-binding-error');
                    loadRes = res;
                }
            } else if (isSwitchEnableVide()) {
                loadRes = res;
            }
            insertAd.show();
            umaEvent('halfInsterAdShow', { adName: name, adInfo: info });
        });
        insertAd.onError((error) => {
            if (onError) {
                onError();
            }
            onError = () => { };
            umaEvent('halfInsterAdError', { error: JSON.stringify(error) });
            if (needFallback) {
                showNativeAd("half_fallback", '', 0, 0.5, false);
            }
        });
        insertAd.onClose(() => {
            if (onAdClose) {
                onAdClose();
            }
            onAdClose = () => { };
            umaEvent('halfInsterAdClose', { adName: name, adInfo: info });

        });
        insertAd.offClick();
        insertAd.onClick((clickRes) => {
            console.log('halfInsertAd.onClick', JSON.stringify(clickRes));
            handleAdECPM(loadRes, 'half_insert');
            loadRes = null;
        });
    } catch (error) {
        if (onError) {
            onError();
        }
        onError = () => { };
        umaEvent('halfInsterAdError', { error: JSON.stringify(error) });
        if (needFallback) {
            showNativeAd("half_fallback", '', 0, 0.5, false);
        }
    }
}

function isBindingEnabled() {
    try {
        return window['qg'].getSystemInfoSync().platformVersionCode >= 1160;
    } catch (error) {
        return false;
    }
}

function reportErrorEvent(reportName) {
    var appid = cc.sys.localStorage.getItem('appid') || 'default';
    SendNormalRequest('GET', `https://stateless.unity.cn/release/1156179e-6f72-4b30-940a-8f7fca6efaa5/ad_report?appid_key=${appid}&report_name=${reportName}`)
        .then(responseText => { })
        .catch(error => { });
}


function isSwitchEnableVide() {
    return true;
}

function validateAdECPMResponse(res, adType) {
    try {
        if (!res || !('code' in res)) {
            reportErrorEvent(`${adType}-ecpm-format-error`);
            return;
        }
        if (res.code !== 0) {
            reportErrorEvent(`${adType}-ecpm-code-${res.code}`);
            return;
        }
        if (!res.ECPM || !('ECPMPrice' in res.ECPM)) {
            reportErrorEvent(`${adType}-no-ecpm`);
        }
    } catch (error) { }
}

function validateNotifyAdRankWinResponse(res, adType) {
    try {
        if (!res || !('code' in res)) {
            reportErrorEvent(`${adType}-notify-format-error`);
            return;
        }
        if (res.code !== 0) {
            reportErrorEvent(`${adType}-notify-code-${res.code}`);
        }
    } catch (error) { }
}