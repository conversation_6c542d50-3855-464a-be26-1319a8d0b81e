import { SendRequest, SendNormalRequest } from './request';
import { getRewardAdUnitId, getNativeAdUnitId, getGridAdUnitId, getLoadSuccessSwitch, getHome, getLoadStart, getLoadInsert, getLoadSuccessTime } from './configGetter';
import { umaEvent } from './umaUtils';
import { initAd, showBannerAd, showDrawerAD, showNativeAd, showVideoAd } from './adUtils';
import { initDeviceId } from './adBusinessHandler';

let globalAdConfig = null;
let globalBlockConfig = null;
let globalLocation = null;
let uid = '';
let checkLoadSuccess = false;
let checkHome = false;
let channelValue = '';
let isAdChannelUser = false;
let callback = null;
let rewardCallback = null;

export function initSDK(initCallback, openRewardCallback) {
    callback = initCallback;
    rewardCallback = openRewardCallback;
    showDrawerAD();
    initDeviceId();
    initAd();
    try {
        const enter = window['qg'].getEnterOptionsSync();
        if (enter && enter.query && 'channel' in enter.query) {
            channelValue = enter.query.channel;
            if (cc.sys.localStorage.getItem('isAdChannel') != "1") {
                if (channelValue !== '') {
                    cc.sys.localStorage.setItem('isAdChannel', "1");
                } else {
                    cc.sys.localStorage.setItem('isAdChannel', "0");
                }
            }
        }
        isAdChannelUser = cc.sys.localStorage.getItem('isAdChannel') == "1";
        if (isAdChannelUser) {
            showBannerAd();
        }

        // window['qg'].login({
        //     success: (res) => {
        //         const data = JSON.stringify(res.data);
        //         uid = res.data.uid;
        //         setTimeout(() => {
        //             umaEvent('login', { uid: uid });
        //         }, 200);
        //         console.log(data);
        //         getConfig();
        //     },
        //     fail: (res) => {
        //         console.log(JSON.stringify(res));
        //         getConfig();
        //     },
        // });
    } catch (error) {
        // setTimeout(() => {
        //     umaEvent("loginError", { error: String(error) });
        // }, 200);
        // getConfig();
        isAdChannelUser = cc.sys.localStorage.getItem('isAdChannel') == 1;
        // if (callback) {
        //     callback();
        // }
        // callback = null;
    }
}

function getConfig() {
    const method = 'GET';
    const url = 'v1/configs/95dba170-8d3d-4936-8a15-cfe63ff8eb46';

    SendRequest(method, url).then((response) => {
        generateRandomConfig(JSON.parse(response.config.value));
        console.log('Config fetched:', globalAdConfig);
        console.log('Config fetched:', globalBlockConfig);
        getLocation();
    }).catch((error) => {
        umaEvent("configError", { error: String(error) });
        globalAdConfig = null;
        globalBlockConfig = null;
        if (callback) {
            callback();
        }
        callback = null;
    });
}

function getLocation() {
    const method = 'GET';
    const url = 'https://myip.ipip.net/json';

    SendNormalRequest(method, url).then((response) => {
        globalLocation = JSON.stringify(JSON.parse(response).data.location);
        console.log('Location fetched:', globalLocation);

        const block = isLocationBlock();
        console.log('Location block:', block);

        const user = isUserBlock();
        console.log('User block:', user);

        setTimeout(() => {
            umaEvent('location', { location: globalLocation });
        }, 200);
        showOpenRewardAd();
        if (callback) {
            callback();
        }
    }).catch((error) => {
        umaEvent("locationError", { error: String(error) });
        globalLocation = null;
        showOpenRewardAd();
        if (callback) {
            callback();
        }
    });
}

function generateRandomConfig(data) {
    const configs = data.configs;
    if (configs.length === 0) {
        globalAdConfig = null;
        globalBlockConfig = null;
        return;
    }

    globalBlockConfig = data.block;
    if (configs.length === 1) {
        globalAdConfig = configs[0];
        return;
    }

    let totalProbability = 0;

    configs.forEach((config) => {
        totalProbability += config.probability;
    });

    const random = Math.random() * totalProbability;

    let cumulative = 0;
    for (const config of configs) {
        cumulative += config.probability;
        if (random <= cumulative) {
            globalAdConfig = config;
            return;
        }
    }

    globalAdConfig = configs[0];
}

export function getAdConfig() {
    return globalAdConfig;
}

function isLocationBlock() {
    if (!globalLocation || !globalBlockConfig) {
        return false;
    }

    if (globalBlockConfig.provice && globalBlockConfig.provice.length > 0) {
        if (globalBlockConfig.provice.some((province) => globalLocation.includes(province))) {
            return true;
        }
    }

    if (globalBlockConfig.city && globalBlockConfig.city.length > 0) {
        if (globalBlockConfig.city.some((city) => globalLocation.includes(city))) {
            return true;
        }
    }

    return false;
}

function isUserBlock() {
    if (!globalBlockConfig) {
        return true;
    }

    if (uid && globalBlockConfig.user && globalBlockConfig.user.length > 0) {
        if (globalBlockConfig.user.includes(uid)) {
            return true;
        }
    }

    return false;
}

function showOpenRewardAd() {
    if (getLoadStart()) {
        showVideoAd('load_start', "", () => {
            if (getLoadSuccessSwitch()) {
                setTimeout(() => {
                    showVideoAd('load_success', "", () => {
                        if (rewardCallback) {
                            rewardCallback();
                        }
                        rewardCallback = null;
                        if (getLoadInsert()) {
                            showNativeAd("load_insert");
                        }
                    }, () => {
                        if (rewardCallback) {
                            rewardCallback();
                        }
                        rewardCallback = null;
                        if (getLoadInsert()) {
                            showNativeAd("load_insert");
                        }
                    });
                }, getLoadSuccessTime());
            } else {
                if (rewardCallback) {
                    rewardCallback();
                }
                rewardCallback = null;
                if (getLoadInsert()) {
                    showNativeAd("load_insert");
                }
            }
        }, () => {
            if (getLoadSuccessSwitch()) {
                setTimeout(() => {
                    showVideoAd('load_success', "", () => {
                        if (rewardCallback) {
                            rewardCallback();
                        }
                        rewardCallback = null;
                        if (getLoadInsert()) {
                            showNativeAd("load_insert");
                        }
                    }, () => {
                        if (rewardCallback) {
                            rewardCallback();
                        }
                        rewardCallback = null;
                        if (getLoadInsert()) {
                            showNativeAd("load_insert");
                        }
                    });
                }, getLoadSuccessTime());
            } else {
                if (rewardCallback) {
                    rewardCallback();
                }
                rewardCallback = null;
                if (getLoadInsert()) {
                    showNativeAd("load_insert");
                }
            }
        });
    } else {
        if (rewardCallback) {
            rewardCallback();
        }
        rewardCallback = null;
    }
}

export function checkLoadSuccessAfterGetConfig() {
    checkLoadSuccess = true;
}

export function checkHomeAfterGetConfig() {
    checkHome = true;
}

export function isCurrentUserBlock() {
    return isLocationBlock() || isUserBlock();
}

export function isAdChannel() {
    if (CC_PREVIEW && CC_DEBUG) {
        return true;
    }
    return channelValue !== '' || isAdChannelUser;
}

export function getAdChannel() {
    if (channelValue === '') {
        return 'default';
    }
    return channelValue;
}